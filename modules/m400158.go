package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"igameHttp/types/belatra"
	"math/rand"
)

// onegame
// 游戏ID: 400158
// 游戏名称: Cafe50s
// 作者: pasty
// 生成时间: 2025-08-16 15:11:08

// 配置结构体
type c400158 struct {
	MaxPayout     int64              `yaml:"maxPayout"`     // 最大赔付
	Line          int32              `yaml:"line"`          // 线数
	Row           int                `yaml:"row"`           // 行数
	Column        int                `yaml:"column"`        // 列数
	Pattern       [][]basic.Position `yaml:"pattern"`       // 连线模式
	PayoutTable   [][]int16          `yaml:"payoutTable"`   // 赔付表
	IconWeight    map[int16]int32    `yaml:"iconWeight"`    // 图标权重
	WildNumWeight map[int16]int32    `yaml:"wildNumWeight"` // 百搭权重
	WildMulWeight map[int16]int32    `yaml:"wildMulWeight"` // 百搭权重
	WildIcon      int16              `yaml:"wildIcon"`      // 百搭图标
	ScatterIcon   int16              `yaml:"scatterIcon"`   // 散布图标
	MinLimit      map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}

// 游戏模块结构体
var _ = Factory.reg(basic.NewGeneral[*m400158])

type m400158 struct {
	Config                      c400158
	RandByWeight                *utils.RandomWeightPicker[int16, int32]
	RandByWeightNoWild          *utils.RandomWeightPicker[int16, int32]
	RandByWildNumWeight         *utils.RandomWeightPicker[int16, int32]
	RandByWildMulWeight         *utils.RandomWeightPicker[int16, int32]
	RandByWeightNoScatter       *utils.RandomWeightPicker[int16, int32]
	RandByWeightNoWildNoScatter *utils.RandomWeightPicker[int16, int32]
}

func (m *m400158) Init(config []byte) {
	m.Config = utils.ParseYAML[c400158](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
	noWildWeight := make(map[int16]int32)
	for k, v := range m.Config.IconWeight {
		if k != m.Config.WildIcon {
			noWildWeight[k] = v
		}
	}
	noScatterWeight := make(map[int16]int32)
	for k, v := range m.Config.IconWeight {
		if k != m.Config.ScatterIcon {
			noScatterWeight[k] = v
		}
	}
	noWildNoScatterWeight := make(map[int16]int32)
	for k, v := range m.Config.IconWeight {
		if k != m.Config.ScatterIcon && k != m.Config.WildIcon {
			noWildNoScatterWeight[k] = v
		}
	}
	m.RandByWeightNoScatter = utils.NewRandomWeightPicker(noScatterWeight)
	m.RandByWeightNoWildNoScatter = utils.NewRandomWeightPicker(noWildNoScatterWeight)
	m.RandByWildNumWeight = utils.NewRandomWeightPicker(m.Config.WildNumWeight)
	m.RandByWeightNoWild = utils.NewRandomWeightPicker(noWildWeight)
	m.RandByWildMulWeight = utils.NewRandomWeightPicker(m.Config.WildMulWeight)
}

func (m m400158) ID() int32 {
	return 400158
}

func (m m400158) Line() int32 {
	return 20
}

func (m m400158) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m400158) Exception(code int32) string {
	s := &games.S400158{}
	return s.Exception(code)
}

func (m *m400158) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m400158) Spin(rd *rand.Rand) basic.ISpin {

	// 创建旋转结果
	spin := games.S400158{}
	if rd.Int31n(100) < 10 {
		spin.IsRespin = true
	}
	ScatterCount := 0
	if spin.IsRespin {
		pages := m.genarateRespinPages(rd)
		spin.Pages = pages
		spin.Pays = pages[len(pages)-1].Pays
	} else {
		allLineWin := int32(0)
		page, scatterCount := m.genaratePage(rd)
		if scatterCount >= 3 && scatterCount < 5 {
			spin.IsFree = true
			ScatterCount = scatterCount
		} else if scatterCount >= 5 {
			spin.IsHot = true
			ScatterCount = scatterCount
		}
		linesInfo := m.getPayout(page.StopGrid)
		page.Lines = linesInfo
		for _, line := range linesInfo {
			allLineWin += int32(line.Iwin.Win)
		}
		page.Pays = allLineWin
		page.WildMaskGrid = m.makeMaskGrid()
		page.WildMulArrBool = make([]bool, m.Config.Column)
		page.WildMulArr = make([]int16, m.Config.Column)
		if spin.IsFree || spin.IsHot {
			page.FreeInfo = belatra.FreeInfo{
				Total:   7 + scatterCount - 3,
				Remain:  7 + scatterCount - 3,
				Award:   7 + scatterCount - 3,
				Nesting: 1,
				WildDir: 0,
				AllWin:  0,
				FGAux:   0,
			}
		}
		spin.Pages = append(spin.Pages, page)
		spin.Pays = allLineWin
	}
	if spin.IsFree || spin.IsHot {
		freeSpinNum := 7 + ScatterCount - 3
		totalAddFreeSpinNum := 0
		for i := 0; i < freeSpinNum; i++ {
			allLineWin := int32(0)
			Muls := int16(0)
			freePage := m.genarateFreePage(rd, spin.Pages[len(spin.Pages)-1])
			if m.checkWildMulArrBool(freePage.WildMulArrBool) {
				allLineWin = int32(m.Config.MaxPayout)
				freePage.Pays = allLineWin
				spin.MaxPayout = true
				spin.Pages = append(spin.Pages, freePage)
				spin.Pays = freePage.Pays
				break
			} else {
				linesInfo := m.getPayout(freePage.StopGrid)
				freePage.Lines = linesInfo
				for _, line := range linesInfo {
					allLineWin += int32(line.Iwin.Win)
				}
				for _, v := range freePage.WildMulArr {
					Muls += v
				}
				if Muls > 0 {
					allLineWin = allLineWin * int32(Muls)
					freePage.Muls = int32(Muls)
				}
			}
			addFreeSpinNum := m.checkAddFreeSpinNum(spin, freePage)
			totalAddFreeSpinNum += addFreeSpinNum
			freeSpinNum += addFreeSpinNum
			freePage.Pays = allLineWin
			freePage.FreeInfo = belatra.FreeInfo{
				Total:   freeSpinNum,
				Remain:  freeSpinNum - i - 1,
				Nesting: 1 + totalAddFreeSpinNum,
				Award:   addFreeSpinNum,
				WildDir: 0,
				AllWin:  int(allLineWin),
				FGAux:   0,
			}
			spin.Pages = append(spin.Pages, freePage)
			spin.Pays += freePage.Pays
		}
	}
	return &spin
}

func (m *m400158) genaratePage(rd *rand.Rand) (page games.P400158, count int) {
	// TODO: 实现赔付计算逻辑
	// 这里需要根据具体游戏规则实现
	grid, scatterCount := m.generateGrid(rd)
	linesInfo := m.getPayout(grid)
	allPay := int32(0)
	for _, line := range linesInfo {
		allPay += int32(line.Iwin.Win)
	}
	//for _, icons := range grid {
	//	for _, icon := range icons {
	//		if icon == m.Config.ScatterIcon {
	//			count++
	//		}
	//	}
	//}
	page.Pays = allPay
	page.StopGrid = grid
	page.StopGridView = grid
	page.Lines = linesInfo

	return page, scatterCount
}

func (m *m400158) generateGrid(rd *rand.Rand) ([][]int16, int) {
	colScatterCount := make([]int, m.Config.Column)
	grid := make([][]int16, m.Config.Row)
	num := 0
	for i := 0; i < m.Config.Row; i++ {
		grid[i] = make([]int16, m.Config.Column)
		for j := 0; j < m.Config.Column; j++ {
			grid[i][j] = m.RandByWeightNoWild.One(rd)
			if grid[i][j] == m.Config.ScatterIcon {
				num++
				colScatterCount[j]++
			}
			if num > 5 {
				num--
				grid[i][j] = m.RandByWeightNoWildNoScatter.One(rd)
			}
		}
	}
	wildCol := rd.Int31n(5)
	for j := 0; j < m.Config.Column; j++ {
		if colScatterCount[j] == 0 && num < 3 {
			if rd.Int31n(100) < 25 {
				rollWildResult := m.rollWild(rd)
				for i := 0; i < m.Config.Row; i++ {
					if rollWildResult[i] == 99 {
						grid[i][wildCol] = 0
					}
				}
			}
		}
	}

	return grid, num
}

func (m *m400158) genarateRespinPages(rd *rand.Rand) (pages []games.P400158) {
	respinCount := 0
	wildMaskGrid := m.makeMaskGrid()
	firstStopViewGrid, wildMulArr := m.initRespinGrid(rd)
	firsStopGrid := cloneSlice400158(firstStopViewGrid)
	for k, v := range wildMulArr {
		if v == 1 {
			respinCount++
			for i := 0; i < m.Config.Row; i++ {
				firsStopGrid[i][k] = 0
				wildMaskGrid[i][k] = 1
			}
		}
	}
	allPays := int32(0)
	linesInfos := m.getPayout(firsStopGrid)
	for _, line := range linesInfos {
		allPays += int32(line.Iwin.Win)
	}
	page := games.P400158{
		StopGridView: firstStopViewGrid,
		StopGrid:     firsStopGrid,
		WildMaskGrid: wildMaskGrid,
		WildMulArr:   wildMulArr,
		Pays:         allPays,
		Lines:        linesInfos,
		RespinInfo: belatra.RespinInfo{
			NIt:     respinCount,
			Mask:    m.genRespinMask(wildMulArr),
			Total:   respinCount,
			Award:   respinCount,
			Nesting: 0,
		},
	}
	pages = append(pages, page)
	for i := 1; i <= respinCount; i++ {
		newWildMaskGrid := cloneSlice400158(pages[len(pages)-1].WildMaskGrid)
		count := 0
		pays := int32(0)
		stopViewGrid, stopGrid, newWildMulArr := m.generateRespinGrid(rd, pages[len(pages)-1].WildMulArr, pages[len(pages)-1].StopGrid)
		for k, v := range newWildMulArr {
			if v == 1 {
				count++
				for j := 0; j < m.Config.Row; j++ {
					//stopGrid[j][k] = 0
					newWildMaskGrid[j][k] = 1
				}
			}
		}
		award := count - respinCount
		respinCount = count
		linesInfos = m.getPayout(stopGrid)
		for _, line := range linesInfos {
			pays += int32(line.Iwin.Win)
		}
		pages = append(pages, games.P400158{
			StopGridView: stopViewGrid,
			StopGrid:     stopGrid,
			WildMaskGrid: newWildMaskGrid,
			WildMulArr:   newWildMulArr,
			Pays:         pays,
			Lines:        linesInfos,
			RespinInfo: belatra.RespinInfo{
				NIt:     respinCount - i,
				Mask:    m.genRespinMask(newWildMulArr),
				Total:   respinCount,
				Award:   award,
				Nesting: 1,
			},
		})
	}
	return pages
}

func (m *m400158) initRespinGrid(rd *rand.Rand) ([][]int16, []int16) {
	wildMulArr := make([]int16, m.Config.Column)
	grid := make([][]int16, m.Config.Row)
	for i := 0; i < m.Config.Row; i++ {
		grid[i] = make([]int16, m.Config.Column)
		for j := 0; j < m.Config.Column; j++ {
			grid[i][j] = m.RandByWeightNoWildNoScatter.One(rd)
		}
	}
	count := 0
	for {
		for k, v := range wildMulArr {
			if rd.Int31n(100) < 40 && v != 1 {
				wildMulArr[k] = 1
				count++
			}
			if count == 2 {
				break
			}
		}
		if count == 2 {
			break
		}
	}
	for k, v := range wildMulArr {
		if v == 1 {
			rollWildResult := m.rollWild(rd)
			for i := 0; i < m.Config.Row; i++ {
				if rollWildResult[i] == 99 {
					grid[i][k] = 0
				}
			}
		}
	}
	return grid, wildMulArr
}

func (m *m400158) generateRespinGrid(rd *rand.Rand, wildMulArr []int16, stopGrid [][]int16) ([][]int16, [][]int16, []int16) {
	newGrid := cloneSlice400158(stopGrid)
	count := 0
	newWildMulArr := make([]int16, m.Config.Column)
	copy(newWildMulArr, wildMulArr)
	for k, v := range newWildMulArr {
		if v == 0 {
			for i := 0; i < m.Config.Row; i++ {
				newGrid[i][k] = m.RandByWeightNoWildNoScatter.One(rd)
				//if grid[i][k] == 0 && count < 3 {
				//	newWildMulArr[k] = 1
				//}
			}
			count++
		}
	}
	for k, v := range newWildMulArr {
		if v == 0 && count < 3 && rd.Int31n(100) < 5 {
			rollWildResult := m.rollWild(rd)
			newWildMulArr[k] = 1
			for i := 0; i < m.Config.Row; i++ {
				if rollWildResult[i] == 99 {
					newGrid[i][k] = 0
				}
			}
		}
	}
	newStopGrid := cloneSlice400158(newGrid)
	for k, v := range newWildMulArr {
		if v == 1 {
			for i := 0; i < m.Config.Row; i++ {
				newStopGrid[i][k] = 0
			}
		}
	}
	return newGrid, newStopGrid, newWildMulArr
}

func (m *m400158) genRespinMask(wildMulArr []int16) [][]int16 {
	mask := m.makeGrid()
	for k, v := range wildMulArr {
		if v == 1 {
			for i := 0; i < m.Config.Row; i++ {
				mask[i][k] = 0
			}
		}
	}
	return mask
}

func (m *m400158) genarateFreePage(rd *rand.Rand, page games.P400158) (newPage games.P400158) {
	stopGridView, stopGrid, wildMaskGrid, wildMulArrBool, wildMulArr := m.genarateFreeGrid(rd, page.WildMaskGrid, page.WildMulArrBool, page.WildMulArr)
	newPage.StopGridView = stopGridView
	newPage.StopGrid = stopGrid
	newPage.WildMaskGrid = wildMaskGrid
	newPage.WildMulArrBool = wildMulArrBool
	newPage.WildMulArr = wildMulArr
	return newPage
}

func (m *m400158) genarateFreeGrid(rd *rand.Rand, wildMaskGrid [][]int16, wildMulArrBool []bool, wildMulArr []int16) ([][]int16, [][]int16, [][]int16, []bool, []int16) {
	newWildMaskGrid := cloneSlice400158(wildMaskGrid)
	newWildMulArrBool := make([]bool, m.Config.Column)
	copy(newWildMulArrBool, wildMulArrBool)
	newWildMulArr := make([]int16, m.Config.Column)
	copy(newWildMulArr, wildMulArr)
	stopGridView := make([][]int16, m.Config.Row)
	for i := 0; i < m.Config.Row; i++ {
		stopGridView[i] = make([]int16, m.Config.Column)
		for j := 0; j < m.Config.Column; j++ {
			if newWildMaskGrid[i][j] == 0 {
				stopGridView[i][j] = m.RandByWeightNoWildNoScatter.One(rd)
			}
		}
	}
	for k, v := range newWildMulArrBool {
		if !v && rd.Int31n(100) < 5 {
			rollWild := m.rollWild(rd)
			for i := 0; i < m.Config.Row; i++ {
				if rollWild[i] == 99 {
					stopGridView[i][k] = 0
				}
			}
			newWildMulArrBool[k] = true
			newWildMulArr[k] = m.RandByWildNumWeight.One(rd)
		}
	}
	stopGrid := cloneSlice400158(stopGridView)
	for k, v := range newWildMulArrBool {
		if v {
			for i := 0; i < m.Config.Row; i++ {
				stopGrid[i][k] = 0
				newWildMaskGrid[i][k] = 1
			}
		}
	}
	return stopGridView, stopGrid, newWildMaskGrid, newWildMulArrBool, newWildMulArr
}

func (m *m400158) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S400158)
	s.GameId = m.ID()
	s.Line = m.Line()
	s.Row = m.Config.Row
	s.Column = m.Config.Column

	// 最大赔付限制
	if s.Pays > int32(m.Config.MaxPayout) {
		s.Pays = int32(m.Config.MaxPayout)
	}

	return s
}

func (m m400158) getPayout(grid [][]int16) []belatra.LinesInfo {
	linesInfo := []belatra.LinesInfo{}
	for lineID, pattern := range m.Config.Pattern {
		symbols := make([]int16, 5)
		for i, pos := range pattern {
			row := pos.Row()
			col := pos.Column()
			if row < 0 || row >= 4 || col < 0 || col >= 5 {
				continue
			}
			symbols[i] = grid[row][col]
		}
		if ok, payout, online := m.checkLine(symbols); ok {
			linesInfo = append(linesInfo, belatra.LinesInfo{
				ID: lineID,
				Iwin: belatra.Iwin{
					Cost:   20,
					K:      1,
					Win:    payout,
					OnLine: online,
				},
			})
		}
	}
	return linesInfo
}

func (m m400158) checkLine(symbols []int16) (bool, int64, []int16) {
	if len(symbols) < 3 {
		return false, 0, nil
	}

	// 初始化匹配图标和计数
	matchIcon := symbols[0]
	count := 1
	//wildCount := 0

	// 如果第一个图标是 WildIcon，寻找第一个非 WildIcon 作为匹配基准
	if matchIcon == m.Config.WildIcon {
		//wildCount = 1
		for i := 1; i < len(symbols); i++ {
			if symbols[i] != m.Config.WildIcon {
				matchIcon = symbols[i] // 找到第一个非 WildIcon
				break
			}
			//wildCount++
		}
		// 如果全是 WildIcon，按 WildIcon 赔付
		//if wildCount >= 3 {
		//	payout := m.getPayoutTable(symbols, wildCount)
		//	online := make([]int16, len(symbols))
		//	copy(online, symbols[:wildCount])
		//	for i := wildCount; i < len(symbols); i++ {
		//		if online[i] == 0 {
		//			online[i] = 127
		//		}
		//	}
		//	return true, int64(payout), online
		//}
	}

	// 从后续图标开始统计连续匹配
	for i := 1; i < len(symbols); i++ {
		if symbols[i] == matchIcon || symbols[i] == m.Config.WildIcon {
			count++
		} else {
			break
		}
	}
	if count >= 3 {
		payout := m.getPayoutTable(matchIcon, count)
		online := make([]int16, len(symbols))
		copy(online, symbols[:count])
		for i := count; i < len(symbols); i++ {
			if online[i] == 0 {
				online[i] = 127
			}
		}
		return true, int64(payout), online
	}
	return false, 0, nil
}

func (m m400158) getPayoutTable(symbols int16, count int) int16 {
	// 确定实际的匹配图标
	//matchIcon := symbols[0]
	//
	//// 如果第一个图标是wild符号，寻找第一个非wild符号作为匹配基准
	//if matchIcon == m.Config.WildIcon {
	//	for i := 1; i < len(symbols) && i < count; i++ {
	//		if symbols[i] != m.Config.WildIcon {
	//			matchIcon = symbols[i]
	//			break
	//		}
	//	}
	// 如果全是wild符号，使用wild符号的赔率
	//if matchIcon == m.Config.WildIcon {
	//	for _, payout := range m.Config.PayoutTable {
	//		if payout[0] == m.Config.WildIcon && int(payout[1]) == count {
	//			return payout[2]
	//		}
	//	}
	//}
	//}
	// 使用确定的匹配图标查找赔率
	for _, payout := range m.Config.PayoutTable {
		if payout[0] == symbols && int(payout[1]) == count {
			return payout[2]
		}
	}
	return 0
}

func (m m400158) Rule(ctx map[string]any) string {
	gs := map[string]any{
		"analInfo": map[string]any{
			"arrlimits_winLimitK": []int{5000},
			"baseReels":           []any{[]int{6, 5, 5, 5, 10, 5, 8, 8, 8, 8, 10, 9, 9, 9, 9, 4, 4, 4, 4, 10, 7, 7, 7, 2, 2, 2, 2, 6, 6, 6, 10, 6, 3, 3, 3, 3, 7, 7, 10, 7, 7, 4, 4, 4, 4, 8, 8, 8, 8, 10, 9, 9, 9, 9, 2, 2, 2, 2, 10, 7, 7, 7, 7, 4, 4, 4, 4, 6, 6, 6, 6, 1, 1, 1, 1, 6, 6, 9, 9, 10, 8, 8, 8, 2, 2, 2, 2, 7, 7, 7, 7, 10, 6, 6, 9, 9, 5, 5, 5, 5, 6, 6, 8, 8, 10, 7, 7, 7, 7, 4, 4, 4, 4, 9, 9, 9, 10, 8, 8, 8, 1, 1, 1, 1, 7, 7, 7, 7, 3, 3, 3, 3, 6, 6, 6, 6, 5, 5, 5, 5, 6, 6, 6, 6, 3, 3, 3, 3, 7, 7, 7, 7, 5, 5, 5, 5, 6, 6, 6, 6, 4, 4, 4, 4, 6, 6, 6, 10, 7, 7, 7, 5, 5, 5, 5, 6, 6, 6, 6, 3, 3, 3, 3, 7, 7, 7, 7, 4, 4, 6, 6, 8, 9, 2, 4, 7, 7, 7, 10, 6, 6, 6, 5, 9, 8, 2, 2, 9, 8, 5, 2, 9, 8, 4, 7, 6}, []int{4, 3, 7, 7, 5, 3, 4, 4, 5, 3, 10, 8, 8, 7, 7, 7, 2, 2, 2, 10, 6, 6, 6, 6, 9, 9, 9, 9, 4, 3, 7, 7, 5, 3, 2, 7, 8, 8, 10, 7, 7, 7, 4, 5, 3, 2, 2, 10, 4, 7, 9, 9, 4, 7, 7, 6, 6, 6, 3, 3, 7, 7, 7, 7, 2, 2, 2, 8, 8, 8, 8, 9, 9, 9, 9, 4, 4, 7, 7, 7, 7, 5, 5, 5, 8, 8, 8, 4, 4, 6, 6, 6, 6, 2, 2, 2, 9, 9, 9, 5, 5, 2, 2, 2, 3, 3, 6, 6, 6, 6, 1, 1, 1, 8, 8, 8, 8, 5, 5, 5, 9, 9, 9, 1, 1, 1, 6, 6, 6, 6, 5, 5, 5, 6, 6, 6, 6, 1, 1, 9, 9, 9, 9, 4, 4, 8, 8, 8, 8, 6, 6, 6, 6, 8, 8, 8, 8, 5, 5, 5, 9, 9, 9, 9, 2, 2, 2, 6, 6, 6, 6, 8, 8, 8, 8, 2, 2, 2, 9, 9, 9, 9, 5, 5, 5, 6, 6, 6, 6, 4, 4, 8, 8, 8, 1, 1, 9, 9, 9, 9, 5, 5, 5, 8, 8, 8, 1, 1, 6, 6, 6, 6, 2, 2, 2, 9, 9, 9, 9, 10, 8, 8, 8, 1, 1, 3, 2, 2, 2}, []int{7, 2, 5, 10, 4, 4, 2, 6, 6, 6, 5, 2, 4, 4, 10, 5, 6, 6, 2, 2, 5, 6, 6, 2, 4, 4, 6, 6, 3, 5, 9, 10, 9, 9, 8, 8, 8, 8, 2, 2, 7, 7, 7, 7, 8, 8, 8, 7, 7, 7, 7, 1, 1, 6, 6, 6, 4, 4, 3, 3, 3, 4, 4, 4, 7, 7, 7, 7, 9, 9, 10, 9, 3, 3, 3, 6, 6, 6, 6, 3, 3, 3, 4, 4, 4, 7, 7, 7, 9, 9, 9, 3, 3, 7, 7, 10, 7, 7, 1, 1, 1, 8, 8, 8, 5, 5, 4, 4, 7, 7, 7, 7, 9, 9, 9, 9, 4, 4, 4, 8, 8, 8, 2, 2, 4, 4, 4, 6, 6, 6, 6, 1, 1, 1, 10, 7, 7, 7, 4, 4, 4, 5, 5, 2, 2, 3, 3, 3, 8, 8, 8, 10, 7, 7, 7, 2, 2, 9, 9, 9, 9, 4, 4, 4, 9, 9, 9, 3, 3, 3, 8, 8, 8, 8, 1, 1, 1, 7, 7, 7, 7, 10, 8, 8, 8, 5, 5, 9, 9, 9, 9, 1, 1, 1, 7, 7, 7, 2, 2, 9, 9, 9, 10, 8, 8, 8, 8, 6, 6, 6, 6, 1, 1, 4, 4, 2, 2, 3, 3, 3, 7}, []int{9, 2, 2, 2, 10, 6, 6, 6, 6, 1, 1, 1, 8, 8, 8, 5, 5, 5, 2, 2, 7, 7, 7, 9, 9, 9, 5, 5, 2, 2, 2, 10, 8, 8, 8, 1, 1, 1, 6, 6, 6, 6, 1, 1, 1, 9, 9, 9, 2, 2, 2, 10, 7, 7, 7, 7, 9, 9, 9, 4, 4, 2, 2, 2, 7, 7, 2, 2, 2, 10, 6, 6, 6, 5, 5, 7, 7, 7, 7, 3, 3, 3, 6, 6, 6, 6, 4, 4, 4, 6, 6, 6, 10, 3, 3, 3, 7, 7, 7, 7, 2, 2, 2, 9, 9, 9, 9, 5, 5, 5, 6, 6, 6, 6, 5, 5, 7, 7, 7, 4, 4, 4, 8, 8, 8, 4, 4, 4, 6, 6, 6, 6, 10, 9, 9, 9, 4, 4, 4, 8, 8, 8, 8, 1, 1, 1, 9, 9, 9, 3, 3, 3, 6, 6, 6, 6, 5, 5, 8, 8, 8, 4, 4, 4, 6, 6, 6, 6, 5, 5, 8, 8, 8, 8, 6, 6, 6, 3, 3, 3, 7, 10, 7, 7, 7, 4, 4, 4, 7, 7, 7, 3, 3, 3, 6, 6, 6, 6, 6, 3, 3, 3, 8, 8, 8, 5, 5, 9, 9, 9, 9, 1, 1, 1, 8, 8, 8, 8, 2, 2, 2, 9, 9, 9}, []int{1, 5, 5, 5, 5, 10, 7, 7, 7, 7, 4, 4, 4, 4, 7, 7, 7, 7, 3, 3, 3, 3, 10, 8, 8, 8, 8, 2, 2, 2, 2, 6, 6, 6, 6, 4, 4, 4, 4, 2, 2, 8, 8, 8, 8, 6, 6, 9, 9, 10, 9, 9, 4, 4, 4, 4, 7, 7, 7, 7, 1, 1, 1, 1, 9, 9, 9, 9, 1, 1, 1, 1, 8, 8, 8, 8, 10, 4, 4, 4, 4, 6, 6, 6, 6, 3, 3, 3, 3, 6, 6, 6, 6, 4, 4, 4, 4, 6, 6, 6, 6, 5, 5, 5, 5, 6, 6, 6, 6, 2, 2, 2, 2, 7, 7, 7, 7, 4, 4, 4, 4, 6, 6, 8, 8, 3, 3, 3, 3, 7, 7, 7, 10, 7, 5, 5, 5, 5, 7, 7, 7, 7, 2, 2, 2, 2, 7, 7, 7, 7, 3, 3, 3, 3, 6, 6, 9, 9, 3, 3, 3}},
			"formula": map[string]any{
				"args": []string{"betPerLine", "nlines"},
				"body": "return(betPerLine * nlines/1)",
			},
			"formulaReverse": map[string]any{
				"args": []string{"betPerGame", "nlines"},
				"body": "return(betPerGame / nlines*1)",
			},
			"freeReels":         []any{[]int{6, 5, 5, 5, 5, 8, 8, 8, 8, 9, 9, 9, 9, 4, 4, 4, 4, 7, 7, 7, 2, 2, 2, 2, 3, 3, 3, 7, 7, 7, 7, 4, 4, 4, 4, 8, 8, 8, 8, 9, 9, 9, 9, 2, 2, 2, 2, 7, 7, 7, 7, 4, 4, 4, 4, 1, 1, 1, 1, 6, 6, 9, 9, 8, 8, 8, 2, 2, 2, 2, 7, 7, 7, 7, 6, 6, 9, 9, 5, 5, 5, 5, 6, 6, 8, 8, 7, 7, 7, 7, 4, 4, 4, 4, 9, 9, 9, 8, 8, 8, 1, 1, 1, 1, 7, 7, 7, 7, 3, 3, 3, 3, 6, 6, 6, 6, 5, 5, 5, 5, 6, 6, 6, 6, 3, 3, 3, 3, 7, 7, 7, 7, 5, 5, 5, 5, 6, 6, 6, 6, 4, 4, 4, 4, 6, 6, 6, 7, 7, 7, 5, 5, 5, 5, 6, 6, 6, 6, 3, 3, 3, 3, 7, 7, 7, 7, 4, 4, 6, 6, 8, 9, 2, 4, 7, 7, 7, 6, 6, 6, 5, 9, 8, 2, 2, 9, 8, 5, 2, 9, 8, 4, 7, 6}, []int{4, 3, 7, 7, 5, 3, 4, 4, 5, 3, 8, 8, 7, 7, 7, 2, 2, 2, 6, 6, 6, 6, 9, 9, 9, 4, 3, 7, 7, 5, 3, 2, 7, 8, 8, 7, 7, 7, 4, 5, 3, 2, 2, 4, 7, 9, 9, 4, 7, 7, 6, 6, 6, 3, 3, 7, 7, 7, 7, 2, 2, 2, 8, 8, 8, 8, 9, 9, 9, 9, 4, 4, 7, 7, 7, 7, 5, 5, 5, 8, 8, 8, 4, 4, 6, 6, 6, 6, 2, 2, 2, 9, 9, 9, 5, 5, 2, 2, 2, 3, 3, 6, 6, 6, 6, 1, 1, 1, 8, 8, 8, 8, 5, 5, 5, 9, 9, 9, 1, 1, 1, 6, 6, 6, 6, 5, 5, 5, 6, 6, 6, 6, 1, 1, 9, 9, 9, 9, 4, 4, 8, 8, 8, 8, 6, 6, 6, 6, 8, 8, 8, 8, 5, 5, 5, 9, 9, 9, 9, 2, 2, 2, 6, 6, 6, 6, 8, 8, 8, 8, 2, 2, 2, 9, 9, 9, 9, 5, 5, 5, 6, 6, 6, 6, 4, 4, 8, 8, 8, 1, 1, 9, 9, 9, 9, 5, 5, 5, 8, 8, 8, 1, 1, 6, 6, 6, 6, 2, 2, 2, 9, 9, 9, 9, 10, 8, 8, 8, 1, 1, 3, 2, 2, 2}, []int{7, 2, 5, 4, 4, 2, 6, 6, 6, 5, 2, 4, 4, 5, 6, 6, 2, 2, 5, 6, 6, 2, 4, 4, 6, 6, 3, 5, 9, 9, 9, 8, 8, 8, 8, 2, 2, 7, 7, 7, 7, 8, 8, 8, 7, 7, 7, 7, 1, 1, 6, 6, 6, 4, 4, 3, 3, 3, 4, 4, 4, 7, 7, 7, 7, 9, 9, 9, 3, 3, 3, 6, 6, 6, 6, 3, 3, 3, 4, 4, 4, 7, 7, 7, 9, 9, 9, 3, 3, 7, 7, 7, 7, 1, 1, 1, 8, 8, 8, 5, 5, 4, 4, 7, 7, 7, 7, 9, 9, 9, 9, 4, 4, 4, 8, 8, 8, 2, 2, 4, 4, 4, 6, 6, 6, 6, 1, 1, 1, 7, 7, 7, 4, 4, 4, 5, 5, 2, 2, 3, 3, 3, 8, 8, 8, 7, 7, 7, 2, 2, 9, 9, 9, 9, 4, 4, 4, 9, 9, 9, 3, 3, 3, 8, 8, 8, 8, 1, 1, 1, 7, 7, 7, 7, 8, 8, 8, 5, 5, 9, 9, 9, 9, 1, 1, 1, 7, 7, 7, 2, 2, 9, 9, 9, 8, 8, 8, 8, 6, 6, 6, 6, 1, 1, 4, 4, 2, 2, 3, 3, 3, 7}, []int{9, 2, 2, 2, 6, 6, 6, 6, 1, 1, 1, 8, 8, 8, 5, 5, 5, 2, 2, 7, 7, 7, 9, 9, 9, 5, 5, 2, 2, 2, 8, 8, 8, 1, 1, 1, 6, 6, 6, 6, 1, 1, 1, 9, 9, 9, 2, 2, 2, 7, 7, 7, 7, 9, 9, 9, 4, 4, 2, 2, 2, 7, 7, 7, 2, 2, 2, 6, 6, 6, 5, 5, 7, 7, 7, 7, 3, 3, 3, 6, 6, 6, 6, 4, 4, 4, 6, 6, 6, 3, 3, 3, 7, 7, 7, 7, 2, 2, 2, 9, 9, 9, 9, 5, 5, 5, 6, 6, 6, 6, 5, 5, 7, 7, 7, 4, 4, 4, 8, 8, 8, 4, 4, 4, 6, 6, 6, 6, 9, 9, 9, 4, 4, 4, 8, 8, 8, 8, 1, 1, 1, 9, 9, 9, 3, 3, 3, 6, 6, 6, 6, 5, 5, 8, 8, 8, 4, 4, 4, 6, 6, 6, 6, 5, 5, 8, 8, 8, 8, 6, 6, 6, 3, 3, 3, 7, 7, 7, 7, 4, 4, 4, 7, 7, 7, 3, 3, 3, 6, 6, 6, 6, 5, 5, 5, 7, 7, 7, 4, 4, 4, 1, 2, 2, 2, 4, 4, 4, 9, 9, 9, 4, 4, 4, 7, 7, 7, 7, 5, 5, 5, 6, 6, 6, 6, 3, 3, 3, 8, 8, 8, 5, 5, 9, 9, 9, 9, 1, 1, 1, 8, 8, 8, 8, 2, 2, 2, 9, 9, 9}, []int{1, 5, 5, 5, 5, 7, 7, 7, 7, 4, 4, 4, 4, 7, 7, 7, 7, 3, 3, 3, 3, 8, 8, 8, 8, 2, 2, 2, 2, 6, 6, 6, 6, 4, 4, 4, 4, 2, 2, 8, 8, 8, 8, 6, 6, 9, 9, 9, 9, 4, 4, 4, 4, 7, 7, 7, 7, 1, 1, 1, 1, 9, 9, 9, 9, 1, 1, 1, 1, 8, 8, 8, 8, 4, 4, 4, 4, 6, 6, 6, 6, 3, 3, 3, 3, 6, 6, 6, 6, 4, 4, 4, 4, 6, 6, 6, 6, 5, 5, 5, 5, 6, 6, 6, 6, 2, 2, 2, 2, 7, 7, 7, 7, 4, 4, 4, 4, 6, 6, 8, 8, 3, 3, 3, 3, 7, 7, 7, 10, 7, 5, 5, 5, 5, 7, 7, 7, 7, 2, 2, 2, 2, 7, 7, 7, 7, 3, 3, 3, 3, 6, 6, 9, 9, 3, 3, 3}},
			"lineStyles":        []any{[]int{2, 2, 2, 2, 2}, []int{1, 1, 1, 1, 1}, []int{3, 3, 3, 3, 3}, []int{0, 0, 0, 0, 0}, []int{0, 1, 2, 1, 0}, []int{3, 2, 1, 2, 3}, []int{1, 2, 3, 2, 1}, []int{2, 1, 0, 1, 2}, []int{3, 3, 1, 3, 3}, []int{0, 0, 2, 0, 0}, []int{1, 1, 3, 1, 1}, []int{2, 2, 0, 2, 2}, []int{1, 1, 0, 1, 1}, []int{2, 2, 3, 2, 2}, []int{0, 0, 1, 0, 0}, []int{3, 3, 2, 3, 3}, []int{0, 1, 0, 1, 0}, []int{3, 2, 3, 2, 3}, []int{1, 0, 1, 0, 1}, []int{2, 3, 2, 3, 2}},
			"minScatters":       []int{3},
			"minScattersRespin": 2,
			"outRates_vipmode":  96.2,
			"sasAdditionalId":   "CFSO",
			"sasPaytableId":     "CFS960",
			"scatterIds":        []int{10},
			"statTablo": map[string]any{
				"bigwin":     9,
				"bonus":      7,
				"epicwin":    8,
				"rtp":        96.16,
				"show":       1,
				"volatility": 8,
			},
			"symbolNames": []string{"wild", "shogun", "geisha", "samurai", "mask_wolf", "mask_cat", "coin_clouds", "coin_sakura", "coin_lotus", "coin_knot", "bonus"},
			"volatility":  5,
			"wildIds":     []int{0},
		},
		"antiDynamiteBet": nil,
		"aux":             0,
		"betAssortment":   ctx["betAssortment"],
		"betPerGame":      ctx["input"],
		"betPerLine":      ctx["betPerLine"],
		"buyBonus": map[string]any{
			"buyTotalBetK": []map[string]any{{
				"cost":    100,
				"id":      0,
				"prefix2": "_BASE_FG",
				"rtp":     96.25,
			}, {
				"cost":    400,
				"id":      1,
				"prefix2": "_BASE_SC_5",
				"rtp":     96.55,
			}, {
				"cost":    8,
				"id":      2,
				"prefix2": "_BASE_GIRLS_2",
				"rtp":     96.51,
			}},
			"selectId": -1,
			"wasBuy":   0,
		},
		"denomAssortment_cents":  []int{1},
		"doubleActive":           "off",
		"doubleActiveDbSettings": "off",
		"doubleAssortment":       []string{"off"},
		"dramshow":               nil,
		"gamegroup":              "base",
		"gcurrency":              "",
		"gdenom":                 1,
		"helpInfo": map[string]any{
			"doubles": []any{[]any{"off", 0, 0}},
			"fg": map[string]any{
				"firstAward":    7,
				"limit":         14,
				"nestingAward":  1,
				"portions":      7,
				"respinPortion": 1,
			},
			"paytable": []any{[]any{[]int{0, 1}}, []any{[]int{1, 4}, []int{5, 90}, []int{4, 80}, []int{3, 40}}, []any{[]int{2, 4}, []int{5, 80}, []int{4, 70}, []int{3, 30}}, []any{[]int{3, 4}, []int{5, 80}, []int{4, 40}, []int{3, 30}}, []any{[]int{4, 4}, []int{5, 40}, []int{4, 7}, []int{3, 2}}, []any{[]int{5, 4}, []int{5, 40}, []int{4, 7}, []int{3, 2}}, []any{[]int{6, 4}, []int{5, 20}, []int{4, 5}, []int{3, 1}}, []any{[]int{7, 4}, []int{5, 20}, []int{4, 5}, []int{3, 1}}, []any{[]int{8, 4}, []int{5, 20}, []int{4, 5}, []int{3, 1}}, []any{[]int{9, 4}, []int{5, 20}, []int{4, 5}, []int{3, 1}}, []any{[]int{10, 8}}},
		},
		"helpseed":                true,
		"isMaxFlag":               0,
		"isMaxFlag_lines":         0,
		"linesAssortment":         []int{20},
		"linesPerCredit":          1,
		"maxBetPerGame_cents":     nil,
		"maxBetPerGame_credits":   5000,
		"minBetPerGame_cents":     nil,
		"nlines":                  20,
		"outRatesVolatility":      nil,
		"phaseCur":                "finished",
		"phaseNext":               "toIdle",
		"placedbet":               ctx["input"],
		"present":                 "no",
		"reelstate":               0,
		"setVip_inFreeSpinAlways": -1,
		"startBox": [][]int{
			{3, 0, 0, 0, 1},
			{3, 0, 0, 0, 1},
			{3, 0, 0, 0, 1},
			{3, 0, 0, 0, 1},
		},
		"stopBox": []any{[]int{7, 5, 6, 4, 6}, []int{7, 8, 6, 4, 1}, []int{7, 10, 3, 6, 8}, []int{3, 8, 5, 6, 2}},
		"tmpWin":  200,
		"versions": map[string]any{
			"server_core": "1.1",
			"server_game": "1.0",
			"server_math": "1.0",
		},
		"vipMode": map[string]any{
			"on":             0,
			"vipBetK":        1.25,
			"vip_noSpecSeed": true,
			"wasBuyVip":      0,
		},
		"wildMulArr": []int{0, 0, 0, 0, 0},
		"winValidation": map[string]any{
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
			"needcheck":                    false,
			"period":                       86400000,
			"remaintime":                   86400000,
			"winlimit_fictiveRotate_gcurr": 25000000,
		},
		"winlimits": map[string]any{
			"maxWinLimitK":         5000,
			"maxWin_gcurr":         nil,
			"needControlJackpot":   false,
			"winLimitK_gameconfig": 5000,
		},
		"info_adv_games": map[string]any{
			"hint": 0,
			"list": []any{},
			"nnew": 0,
		},
		"newOpenGames": []any{},
		"ss":           map[string]any{},
		"useracc": map[string]any{
			"altcurr":      "",
			"amount":       91960,
			"currency":     "",
			"currencyType": 2,
			"currencyUnit": 1,
			"symbol_first": "0",
		},
		"uservars": map[string]any{
			"language": "en",
		},
	}
	b, _ := json.Marshal(gs)
	return string(b)
}

func (m m400158) InputCoef(ctl int32) int32 {
	switch ctl {
	case 1:
		return 10000
	case 2:
		return 40000
	case 3:
		return 800
	case 4:
		return 125
	default:
		return 100
	}
}

func (m m400158) MinPayout(ctl int32) int32 {
	mode, ok := m.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * m.Line()
}

func (m *m400158) rollWild(rd *rand.Rand) []int16 {
	wildNum := m.RandByWildNumWeight.One(rd)
	result := make([]int16, 4)

	// 随机决定填充方向（0：从开头，1：从结尾）
	direction := rd.Intn(2)

	// 根据方向计算起始和结束索引
	start := 0
	end := int(wildNum)
	if direction == 1 {
		start = 4 - int(wildNum)
		end = 4
	}

	// 在选定范围内填充1
	for i := start; i < end; i++ {
		result[i] = 99
	}

	return result
}

func cloneSlice400158(src [][]int16) [][]int16 {
	dst := make([][]int16, len(src))
	for i := range src {
		dst[i] = make([]int16, len(src[i]))
		copy(dst[i], src[i])
	}
	return dst
}

func (m m400158) makeGrid() [][]int16 {
	gridRows := make([][]int16, m.Config.Row)
	for row := 0; row < m.Config.Row; row++ {
		gridRows[row] = make([]int16, m.Config.Column)
		for col := 0; col < m.Config.Column; col++ {
			gridRows[row][col] = 126
		}
	}
	return gridRows
}

func (m m400158) makeMaskGrid() [][]int16 {
	gridRows := make([][]int16, m.Config.Row)
	for row := 0; row < m.Config.Row; row++ {
		gridRows[row] = make([]int16, m.Config.Column)
		for col := 0; col < m.Config.Column; col++ {
			gridRows[row][col] = 0
		}
	}
	return gridRows
}

func (m m400158) checkWildMulArrBool(wildMulArrBool []bool) bool {
	for _, v := range wildMulArrBool {
		if !v {
			return false
		}
	}
	return true
}

func (m m400158) checkAddFreeSpinNum(spin games.S400158, page games.P400158) int {
	count := 0
	for _, v := range spin.Pages[len(spin.Pages)-1].WildMulArrBool {
		if v {
			count--
		}
	}
	for _, v := range page.WildMulArrBool {
		if v {
			count++
		}
	}
	return count
}
