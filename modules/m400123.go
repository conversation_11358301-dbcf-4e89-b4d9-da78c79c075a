package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"igameHttp/types/belatra"
	"math/rand"
)

// newgame
// 游戏ID: 400123
// 游戏名称: WildFruitJam
// 作者: pasty
// 生成时间: 2025-08-11 10:40:40

// 配置结构体
type c400123 struct {
	MaxPayout       int64              `yaml:"maxPayout"`   // 最大赔付
	Line            int32              `yaml:"line"`        // 线数
	Row             int                `yaml:"row"`         // 行数
	Column          int                `yaml:"column"`      // 列数
	Pattern25       [][]basic.Position `yaml:"pattern25"`   // 连线模式
	Pattern50       [][]basic.Position `yaml:"pattern50"`   // 连线模式
	Pattern100      [][]basic.Position `yaml:"pattern100"`  // 连线模式
	PayoutTable     map[int16][]int32  `yaml:"payoutTable"` // 赔付表
	IconWeight      map[int16]int32    `yaml:"iconWeight"`  // 图标权重
	MulsWeight      map[int32]int32    `yaml:"mulsWeight"`  // 倍率权重
	WildIcon        int16              `yaml:"wildIcon"`    // 百搭图标
	ScatterIconBell int16              `yaml:"scatterIcon"` // 散布图标
	ScatterIconStar int16
	MinLimit        map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}

// 游戏模块结构体
var _ = Factory.reg(basic.NewGeneral[*m400123])

type m400123 struct {
	Config                      c400123
	RandByWeight                *utils.RandomWeightPicker[int16, int32]
	RandByNoWildWeight          *utils.RandomWeightPicker[int16, int32]
	RandByNoScatterNoWildWeight *utils.RandomWeightPicker[int16, int32]
	RandByMulsWeight            *utils.RandomWeightPicker[int32, int32]
}

func (m *m400123) Init(config []byte) {
	m.Config = utils.ParseYAML[c400123](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
	noWild := make(map[int16]int32)
	for key, weight := range m.Config.IconWeight {
		if key != m.Config.WildIcon {
			noWild[key] = weight
		}
	}
	noScatterNoWild := make(map[int16]int32)
	for key, weight := range m.Config.IconWeight {
		if key != m.Config.ScatterIconBell && key != m.Config.ScatterIconStar && key != m.Config.WildIcon {
			noScatterNoWild[key] = weight
		}
	}
	m.RandByNoScatterNoWildWeight = utils.NewRandomWeightPicker(noScatterNoWild)
	m.RandByNoWildWeight = utils.NewRandomWeightPicker(noWild)
	m.RandByMulsWeight = utils.NewRandomWeightPicker(m.Config.MulsWeight)
}

func (m m400123) ID() int32 {
	return 400123
}

func (m m400123) Line() int32 {
	return 100
}

func (m m400123) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m400123) Exception(code int32) string {
	s := &games.S400123{}
	return s.Exception(code)
}

func (m *m400123) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m400123) Spin(rd *rand.Rand) basic.ISpin {
	// 生成网格
	gridView := m.generateGrid(rd)
	gridStop, gridMask := m.generateMask(gridView)
	// 创建旋转结果
	spin := &games.S400123{
		GridView: gridView,
		GridMask: gridMask,
		GridStop: gridStop,
		Pays:     0,
	}
	//// 计算赔付
	//if rd.Int31n(100) < 10 {
	//	spin.IsLine50 = true
	//} else if rd.Int31n(100) > 90 {
	//	spin.IsLine100 = true
	//}
	m.calculatePayout(rd, spin)

	return spin
}

// 生成二维数组网格
func (m *m400123) generateGrid(rd *rand.Rand) [][]int16 {
	scatterBell := 0
	scatterStar := 0
	grid := make([][]int16, m.Config.Row)
	for i := 0; i < m.Config.Row; i++ {
		grid[i] = make([]int16, m.Config.Column)
		for j := 0; j < m.Config.Column; j++ {
			if j == 0 || j == 4 {
				grid[i][j] = m.RandByNoWildWeight.One(rd)
			} else {
				grid[i][j] = m.RandByWeight.One(rd)
			}
			if grid[i][j] == m.Config.ScatterIconBell {
				scatterBell++
				if scatterBell > 5 {
					grid[i][j] = m.RandByNoScatterNoWildWeight.One(rd)
				}
			}
			if grid[i][j] == m.Config.ScatterIconStar {
				scatterStar++
				if scatterStar > 3 {
					grid[i][j] = m.RandByNoScatterNoWildWeight.One(rd)
				}
			}
		}
	}
	return grid
}

func (m *m400123) generateMask(gridView [][]int16) ([][]int16, [][]int16) {
	gridMask := m.makeMaskGrid()
	grid := cloneSlice400123(gridView)
	// 首先找出需要mask的列
	columnsToMask := make([]bool, m.Config.Column)
	for i := 0; i < m.Config.Row; i++ {
		for j := 0; j < m.Config.Column; j++ {
			if gridView[i][j] == 0 {
				columnsToMask[j] = true
			}
		}
	}
	// 对需要mask的列进行整列处理
	for i := 0; i < m.Config.Row; i++ {
		for j := 0; j < m.Config.Column; j++ {
			if columnsToMask[j] {
				gridMask[i][j] = 1
				grid[i][j] = 0
			}
		}
	}
	return grid, gridMask
}

func (m *m400123) generateMuls(rd *rand.Rand, gridView [][]int16) (muls []int32, isWild bool) {
	columnsToMask := make([]bool, m.Config.Column)
	isWild = false
	for i := 0; i < m.Config.Row; i++ {
		for j := 0; j < m.Config.Column; j++ {
			if gridView[i][j] == 0 {
				columnsToMask[j] = true
				isWild = true
			}
		}
	}
	muls = []int32{-1, -1, -1, -1, -1}
	for i := 0; i < 5; i++ {
		if columnsToMask[i] {
			muls[i] = m.RandByMulsWeight.One(rd)
		}
	}
	return muls, isWild
}

func (m *m400123) calculatePayout(rd *rand.Rand, spin *games.S400123) {
	var pattern [][]basic.Position
	pattern = m.Config.Pattern100
	// TODO: 实现赔付计算逻辑
	scatterPay := int32(0)
	allWin := int32(0)
	muls, isWild := m.generateMuls(rd, spin.GridView)
	linesInfo := m.parsePayout(spin.GridStop, spin.GridView, isWild, pattern)
	scatterInfo := m.scatterPayout(spin.GridView)
	if len(scatterInfo) > 0 {
		for _, i := range scatterInfo {
			scatterPay += int32(i.Win)
		}
	}
	spin.Lines = linesInfo
	spin.Muls = muls
	oneMul := int32(0)
	spin.IsWild = isWild
	spin.Scatters = scatterInfo
	for _, line := range linesInfo {
		allWin += int32(line.Iwin.Win)
	}
	for _, mul := range muls {
		if mul >= 0 {
			oneMul += mul
		}
	}
	if oneMul > 0 {
		spin.Mul = oneMul
	} else {
		spin.Mul = 1
	}
	spin.Pays = allWin*spin.Mul + scatterPay
}

func (m m400123) scatterPayout(gridView [][]int16) (scatterInfo []belatra.ScattersInfo) {
	scatterBellCount := 0
	scatterStarCount := 0
	for i := 0; i < 4; i++ {
		for j := 0; j < 5; j++ {
			if gridView[i][j] == m.Config.ScatterIconBell {
				scatterBellCount++
			}
			if gridView[i][j] == m.Config.ScatterIconStar {
				scatterStarCount++
			}
		}
	}

	if bellPay := m.getPayoutTable(m.Config.ScatterIconBell, scatterBellCount); bellPay > 0 {
		scatterInfo = append(scatterInfo, belatra.ScattersInfo{
			ScId: m.Config.ScatterIconBell,
			N:    scatterBellCount,
			Win:  int64(bellPay),
		})
	}

	if starPay := m.getPayoutTable(m.Config.ScatterIconStar, scatterStarCount); starPay > 0 {
		scatterInfo = append(scatterInfo, belatra.ScattersInfo{
			ScId: m.Config.ScatterIconStar,
			N:    scatterStarCount,
			Win:  int64(starPay),
		})
	}

	if len(scatterInfo) > 0 {
		return scatterInfo
	} else {
		return nil
	}
}

func (m m400123) parsePayout(gridStop [][]int16, gridView [][]int16, isWild bool, pattern [][]basic.Position) []belatra.LinesInfo {
	linesInfo := []belatra.LinesInfo{}
	for lineID, onePattern := range pattern {
		symbols := make([]int16, 5)
		symbolsView := make([]int16, 5)
		for i, pos := range onePattern {
			row := pos.Row()
			col := pos.Column()
			if row < 0 || row >= 4 || col < 0 || col >= 5 {
				continue
			}
			symbols[i] = gridStop[row][col]
			symbolsView[i] = gridView[row][col]
		}
		if ok, payout, online := m.checkLine(symbolsView); ok && !isWild {
			linesInfo = append(linesInfo, belatra.LinesInfo{
				ID: lineID,
				Iwin: belatra.Iwin{
					Cost:   20,
					K:      1,
					Win:    payout,
					OnLine: online,
				},
			})
		} else if ok, payout, online := m.checkLine(symbols); ok && isWild {
			linesInfo = append(linesInfo, belatra.LinesInfo{
				ID: lineID,
				Iwin: belatra.Iwin{
					Cost:   20,
					K:      1,
					Win:    payout,
					OnLine: online,
				},
			})
		}
	}
	return linesInfo
}

func (m m400123) checkLine(symbols []int16) (bool, int64, []int16) {
	if len(symbols) < 3 {
		return false, 0, nil
	}
	count := 1
	matchIcon := symbols[0]
	if matchIcon == m.Config.ScatterIconBell || matchIcon == m.Config.ScatterIconStar {
		return false, 0, nil
	}
	// 从后续图标开始统计连续匹配
	for i := 1; i < len(symbols); i++ {
		if symbols[i] == matchIcon || symbols[i] == m.Config.WildIcon {
			count++
		} else {
			break
		}
	}
	if count >= 3 {
		payout := m.getPayoutTable(matchIcon, count)
		online := make([]int16, len(symbols))
		copy(online, symbols[:count])
		for i := count; i < len(symbols); i++ {
			if online[i] == 0 {
				online[i] = 127
			}
		}
		return true, int64(payout), online
	}
	return false, 0, nil
}

func (m m400123) getPayoutTable(icon int16, index int) int32 {
	for key, payout := range m.Config.PayoutTable {
		if key == icon && index > 0 {
			return payout[index-1]
		}
	}
	return 0
}

func (m *m400123) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S400123)
	s.GameId = m.ID()
	s.Line = m.Line()
	s.Row = m.Config.Row
	s.Column = m.Config.Column

	// 最大赔付限制
	if s.Pays > int32(m.Config.MaxPayout) {
		s.Pays = int32(m.Config.MaxPayout)
	}

	return s
}

func (m m400123) Rule(ctx map[string]any) string {
	//var lineStyles []any
	//switch ctx["nlines"] {
	//case 100:
	//	lineStyles = []any{[]int{2, 2, 2, 2, 2}, []int{1, 1, 1, 1, 1}, []int{3, 3, 3, 3, 3}, []int{0, 0, 0, 0, 0}, []int{0, 1, 2, 1, 0}, []int{1, 2, 3, 2, 1}, []int{2, 1, 0, 1, 2}, []int{3, 2, 1, 2, 3}, []int{1, 1, 0, 1, 1}, []int{2, 2, 3, 2, 2}, []int{0, 0, 1, 0, 0}, []int{3, 3, 2, 3, 3}, []int{0, 1, 2, 3, 3}, []int{3, 2, 1, 0, 0}, []int{0, 0, 1, 2, 3}, []int{3, 3, 2, 1, 0}, []int{1, 0, 0, 0, 0}, []int{0, 1, 1, 1, 1}, []int{3, 2, 2, 2, 2}, []int{2, 3, 3, 3, 3}, []int{3, 3, 1, 3, 3}, []int{0, 0, 2, 0, 0}, []int{1, 1, 3, 1, 1}, []int{2, 2, 0, 2, 2}, []int{0, 1, 0, 1, 0}, []int{3, 2, 3, 2, 3}, []int{1, 0, 1, 0, 1}, []int{2, 3, 2, 3, 2}, []int{0, 1, 1, 1, 0}, []int{3, 2, 2, 2, 3}, []int{2, 3, 3, 3, 2}, []int{1, 0, 0, 0, 1}, []int{2, 1, 2, 1, 2}, []int{1, 2, 1, 2, 1}, []int{0, 3, 3, 3, 0}, []int{3, 0, 0, 0, 3}, []int{1, 2, 2, 2, 1}, []int{2, 1, 1, 1, 2}, []int{0, 3, 0, 3, 0}, []int{3, 0, 3, 0, 3}, []int{1, 3, 3, 3, 1}, []int{2, 0, 0, 0, 2}, []int{0, 2, 0, 2, 0}, []int{3, 1, 3, 1, 3}, []int{1, 3, 1, 3, 1}, []int{2, 0, 2, 0, 2}, []int{0, 0, 3, 0, 0}, []int{3, 3, 0,
	//		3, 3}, []int{1, 1, 2, 1, 1}, []int{2, 2, 1, 2, 2}, []int{0, 2, 2, 2, 0}, []int{3, 1, 1, 1, 3}, []int{1, 0, 3, 0, 1}, []int{2, 3, 0, 3, 2}, []int{0, 3, 1, 3, 0}, []int{3, 0, 2, 0, 3}, []int{1, 3, 2, 3, 1}, []int{2, 0, 1, 0, 2}, []int{0, 1, 1, 1, 2}, []int{3, 2, 2, 2, 1}, []int{1, 2, 2, 2, 3}, []int{2, 1, 1, 1, 0}, []int{1, 2, 2, 2, 2}, []int{2, 1, 1, 1, 1}, []int{0, 0, 1, 2, 2}, []int{3, 3, 2, 1, 1}, []int{1, 1, 2, 3, 3}, []int{2, 2, 1, 0, 0}, []int{0, 0, 0, 1, 2}, []int{3, 3, 3, 2, 1}, []int{0, 1, 1, 2, 2}, []int{3, 2, 2, 1, 1}, []int{1, 2, 2, 3, 3}, []int{2, 1, 1, 0, 0}, []int{0, 0, 1, 1, 2}, []int{3, 3, 2, 2, 1}, []int{1, 1, 2, 2, 3}, []int{2, 2, 1, 1, 0}, []int{1, 1, 1, 2, 1}, []int{2, 2, 2, 1, 0}, []int{0, 0, 1, 1, 1}, []int{3, 3, 2, 2, 2}, []int{1, 1, 2, 2, 2}, []int{2, 2, 1, 1, 1}, []int{2, 2, 3, 3, 3}, []int{1, 1, 0, 0, 0}, []int{0, 0, 1, 1, 0}, []int{3, 3, 2, 2, 3}, []int{1, 1, 2, 2, 1}, []int{2, 2, 1, 1, 2}, []int{2, 2, 3, 3, 2}, []int{1, 1, 0, 0, 1}, []int{0, 1, 1, 0, 0}, []int{3, 2, 2, 3, 3}, []int{1, 2, 2, 1, 1}, []int{2, 1, 1, 2, 2}, []int{2,
	//		3, 3, 2, 2}, []int{1, 0, 0, 1, 1}, []int{0, 3, 2, 1, 0}, []int{3, 0, 1, 2, 3}}
	//case 50:
	//	lineStyles = []any{[]int{2, 2, 2, 2, 2}, []int{1, 1, 1, 1, 1}, []int{3, 3, 3, 3, 3}, []int{0, 0, 0, 0, 0}, []int{0, 1, 2, 1, 0}, []int{1, 2, 3, 2, 1}, []int{2, 1, 0, 1, 2}, []int{3, 2, 1, 2, 3}, []int{1, 1, 0, 1, 1}, []int{2, 2, 3, 2, 2}, []int{0, 0, 1, 0, 0}, []int{3, 3, 2, 3, 3}, []int{0, 1, 2, 3, 3}, []int{3, 2, 1, 0, 0}, []int{0, 0, 1, 2, 3}, []int{3, 3, 2, 1, 0}, []int{1, 0, 0, 0, 0}, []int{0, 1, 1, 1, 1}, []int{3, 2, 2, 2, 2}, []int{2, 3, 3, 3, 3}, []int{3, 3, 1, 3, 3}, []int{0, 0, 2, 0, 0}, []int{1, 1, 3, 1, 1}, []int{2, 2, 0, 2, 2}, []int{0, 1, 0, 1, 0}, []int{3, 2, 3, 2, 3}, []int{1, 0, 1, 0, 1}, []int{2, 3, 2, 3, 2}, []int{0, 1, 1, 1, 0}, []int{3, 2, 2, 2, 3}, []int{2, 3, 3, 3, 2}, []int{1, 0, 0, 0, 1}, []int{2, 1, 2, 1, 2}, []int{1, 2, 1, 2, 1}, []int{0, 3, 3, 3, 0}, []int{3, 0, 0, 0, 3}, []int{1, 2, 2, 2, 1}, []int{2, 1, 1, 1, 2}, []int{0, 3, 0, 3, 0}, []int{3, 0, 3, 0, 3}, []int{1, 3, 3, 3, 1}, []int{2, 0, 0, 0, 2}, []int{0, 2, 0, 2, 0}, []int{3, 1, 3, 1, 3}, []int{1, 3, 1, 3, 1}, []int{2, 0, 2, 0, 2}, []int{0, 0, 3, 0, 0}, []int{3, 3, 0, 3, 3}, []int{1, 1, 2, 1, 1}, []int{2, 2, 1, 2, 2}}
	//case 25:
	//	lineStyles = []any{[]int{2, 2, 2, 2, 2}, []int{1, 1, 1, 1, 1}, []int{3, 3, 3, 3, 3}, []int{0, 0, 0, 0, 0}, []int{0, 1, 2, 1, 0}, []int{1, 2, 3, 2, 1}, []int{2, 1, 0, 1, 2}, []int{3, 2, 1, 2, 3}, []int{1, 1, 0, 1, 1}, []int{2, 2, 3, 2, 2}, []int{0, 0, 1, 0, 0}, []int{3, 3, 2, 3, 3}, []int{0, 1, 2, 3, 3}, []int{3, 2, 1, 0, 0}, []int{0, 0, 1, 2, 3}, []int{3, 3, 2, 1, 0}, []int{1, 0, 0, 0, 0}, []int{0, 1, 1, 1, 1}, []int{3, 2, 2, 2, 2}, []int{2, 3, 3, 3, 3}, []int{3, 3, 1, 3, 3}, []int{0, 0, 2, 0, 0}, []int{1, 1, 3, 1, 1}, []int{2, 2, 0, 2, 2}, []int{0, 1, 0, 1, 0}}
	//}
	gs := map[string]any{
		"analInfo": map[string]any{
			"arrlimits_winLimitK": []int{7500},
			"baseReels":           []any{[]int{7, 7, 7, 7, 6, 6, 6, 6, 1, 1, 1, 1, 2, 2, 2, 2, 6, 6, 6, 6, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 10, 8, 8, 8, 8, 4, 4, 4, 4, 5, 5, 5, 5, 4, 4, 4, 4, 9}, []int{6, 6, 6, 6, 1, 1, 1, 0, 1, 1, 1, 9, 3, 3, 3, 3, 8, 8, 8, 8, 5, 5, 5, 5, 7, 7, 7, 7, 8, 8, 8, 8, 4, 4, 4, 4, 3, 3, 3, 3, 5, 5, 5, 5, 2, 2, 2, 2}, []int{7, 7, 7, 7, 1, 1, 1, 0, 1, 1, 1, 9, 2, 2, 2, 2, 3, 3, 3, 3, 9, 5, 5, 5, 5, 5, 5, 6, 6, 6, 6, 8, 8, 8, 8, 5, 5, 5, 5, 4, 4, 4, 4}, []int{7, 7, 7, 7, 1, 1, 1, 0, 1, 1, 1, 9, 3, 3, 3, 3, 5, 5, 5, 5, 3, 3, 3, 3, 3, 6, 6, 6, 6, 4, 4, 4, 4, 6, 6, 6, 6, 8, 8, 8, 8, 3, 3, 3, 3, 7, 7, 7, 7}, []int{5, 5, 5, 5, 1, 1, 1, 1, 2, 2, 2, 2, 1, 1, 1, 1, 3, 3, 3, 3, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 2, 2, 2, 2, 6, 6, 6, 6, 7, 7, 7, 7, 8, 8, 8, 8}},
			"formula": map[string]any{
				"args": []string{"betPerLine", "nlines"},
				"body": "return(betPerLine * nlines/1)",
			},
			"formulaReverse": map[string]any{
				"args": []string{"betPerGame", "nlines"},
				"body": "return(betPerGame / nlines*1)",
			},
			"lineStyles":      []any{[]int{2, 2, 2, 2, 2}, []int{1, 1, 1, 1, 1}, []int{3, 3, 3, 3, 3}, []int{0, 0, 0, 0, 0}, []int{0, 1, 2, 1, 0}, []int{1, 2, 3, 2, 1}, []int{2, 1, 0, 1, 2}, []int{3, 2, 1, 2, 3}, []int{1, 1, 0, 1, 1}, []int{2, 2, 3, 2, 2}, []int{0, 0, 1, 0, 0}, []int{3, 3, 2, 3, 3}, []int{0, 1, 2, 3, 3}, []int{3, 2, 1, 0, 0}, []int{0, 0, 1, 2, 3}, []int{3, 3, 2, 1, 0}, []int{1, 0, 0, 0, 0}, []int{0, 1, 1, 1, 1}, []int{3, 2, 2, 2, 2}, []int{2, 3, 3, 3, 3}, []int{3, 3, 1, 3, 3}, []int{0, 0, 2, 0, 0}, []int{1, 1, 3, 1, 1}, []int{2, 2, 0, 2, 2}, []int{0, 1, 0, 1, 0}, []int{3, 2, 3, 2, 3}, []int{1, 0, 1, 0, 1}, []int{2, 3, 2, 3, 2}, []int{0, 1, 1, 1, 0}, []int{3, 2, 2, 2, 3}, []int{2, 3, 3, 3, 2}, []int{1, 0, 0, 0, 1}, []int{2, 1, 2, 1, 2}, []int{1, 2, 1, 2, 1}, []int{0, 3, 3, 3, 0}, []int{3, 0, 0, 0, 3}, []int{1, 2, 2, 2, 1}, []int{2, 1, 1, 1, 2}, []int{0, 3, 0, 3, 0}, []int{3, 0, 3, 0, 3}, []int{1, 3, 3, 3, 1}, []int{2, 0, 0, 0, 2}, []int{0, 2, 0, 2, 0}, []int{3, 1, 3, 1, 3}, []int{1, 3, 1, 3, 1}, []int{2, 0, 2, 0, 2}, []int{0, 0, 3, 0, 0}, []int{3, 3, 0, 3, 3}, []int{1, 1, 2, 1, 1}, []int{2, 2, 1, 2, 2}, []int{0, 2, 2, 2, 0}, []int{3, 1, 1, 1, 3}, []int{1, 0, 3, 0, 1}, []int{2, 3, 0, 3, 2}, []int{0, 3, 1, 3, 0}, []int{3, 0, 2, 0, 3}, []int{1, 3, 2, 3, 1}, []int{2, 0, 1, 0, 2}, []int{0, 1, 1, 1, 2}, []int{3, 2, 2, 2, 1}, []int{1, 2, 2, 2, 3}, []int{2, 1, 1, 1, 0}, []int{1, 2, 2, 2, 2}, []int{2, 1, 1, 1, 1}, []int{0, 0, 1, 2, 2}, []int{3, 3, 2, 1, 1}, []int{1, 1, 2, 3, 3}, []int{2, 2, 1, 0, 0}, []int{0, 0, 0, 1, 2}, []int{3, 3, 3, 2, 1}, []int{0, 1, 1, 2, 2}, []int{3, 2, 2, 1, 1}, []int{1, 2, 2, 3, 3}, []int{2, 1, 1, 0, 0}, []int{0, 0, 1, 1, 2}, []int{3, 3, 2, 2, 1}, []int{1, 1, 2, 2, 3}, []int{2, 2, 1, 1, 0}, []int{1, 1, 1, 2, 1}, []int{2, 2, 2, 1, 0}, []int{0, 0, 1, 1, 1}, []int{3, 3, 2, 2, 2}, []int{1, 1, 2, 2, 2}, []int{2, 2, 1, 1, 1}, []int{2, 2, 3, 3, 3}, []int{1, 1, 0, 0, 0}, []int{0, 0, 1, 1, 0}, []int{3, 3, 2, 2, 3}, []int{1, 1, 2, 2, 1}, []int{2, 2, 1, 1, 2}, []int{2, 2, 3, 3, 2}, []int{1, 1, 0, 0, 1}, []int{0, 1, 1, 0, 0}, []int{3, 2, 2, 3, 3}, []int{1, 2, 2, 1, 1}, []int{2, 1, 1, 2, 2}, []int{2, 3, 3, 2, 2}, []int{1, 0, 0, 1, 1}, []int{0, 3, 2, 1, 0}, []int{3, 0, 1, 2, 3}},
			"maxWinFreq_big":  50000000,
			"sasAdditionalId": "WFJ",
			"sasPaytableId":   "WFJ960",
			"scatterIds":      []int{9, 10},
			"statTablo": map[string]any{
				"bigwin":     8,
				"bonus":      8,
				"epicwin":    9,
				"rtp":        96.16,
				"show":       1,
				"volatility": 8,
			},
			"symbolNames": []string{"wild", "seven", "melon", "lemon", "grape", "cherry", "plum", "2bar", "bar", "bell", "star"},
			"volatility":  5,
			"wildIds":     []int{0},
		},
		"antiDynamiteBet": nil,
		"aux":             0,
		"betAssortment":   ctx["betAssortment"],
		"betDependOnLines": []map[string]any{map[string]any{
			"betAssort": []int{1, 2, 3, 4, 5, 6, 8, 10, 15, 20, 30, 40, 60, 80, 100, 120},
			"lines":     25,
		}, map[string]any{
			"betAssort": []int{1, 2, 3, 4, 5, 6, 8, 10, 15, 20, 25, 30, 40, 50, 60},
			"lines":     50,
		}, map[string]any{
			"betAssort": []int{1, 2, 3, 4, 5, 6, 8, 10, 15, 20, 25, 30},
			"lines":     100,
		}},
		"betPerGame":             ctx["input"],
		"betPerLine":             ctx["betPerLine"],
		"buyBonus":               nil,
		"denomAssortment_cents":  []int{1},
		"doubleActive":           "off",
		"doubleActiveDbSettings": "off",
		"doubleAssortment":       []string{"off"},
		"dramshow":               nil,
		"gamegroup":              "base",
		"gcurrency":              "",
		"gdenom":                 1,
		"helpInfo": map[string]any{
			"doubles":  []any{[]any{"off", 0, 0}},
			"paytable": []any{[]any{[]int{0, 1}}, []any{[]int{1, 4}, []int{5, 500}, []int{4, 100}, []int{3, 25}}, []any{[]int{2, 4}, []int{5, 250}, []int{4, 50}, []int{3, 20}}, []any{[]int{3, 4}, []int{5, 250}, []int{4, 50}, []int{3, 20}}, []any{[]int{4, 4}, []int{5, 100}, []int{4, 25}, []int{3, 10}}, []any{[]int{5, 4}, []int{5, 50}, []int{4, 15}, []int{3, 5}}, []any{[]int{6, 4}, []int{5, 50}, []int{4, 15}, []int{3, 5}}, []any{[]int{7, 4}, []int{5, 50}, []int{4, 15}, []int{3, 5}}, []any{[]int{8, 4}, []int{5, 50}, []int{4, 15}, []int{3, 5}}, []any{[]int{9, 8}, []int{5, 50}, []int{4, 10}, []int{3, 2}}, []any{[]int{10, 8}, []int{3, 50}}},
		},
		"isMaxFlag":             0,
		"isMaxFlag_lines":       0,
		"linesAssortment":       []int{25, 50, 100},
		"linesPerCredit":        1,
		"maxBetPerGame_cents":   nil,
		"maxBetPerGame_credits": 3000,
		"minBetPerGame_cents":   nil,
		"nlines":                100,
		"outRatesVolatility":    nil,
		"phaseCur":              "finished",
		"phaseNext":             "toIdle",
		"placedbet":             ctx["input"],
		"present":               "no",
		"reelstate":             0,
		"startBox":              []any{[]int{3, 0, 0, 0, 3}, []int{3, 2, 2, 2, 3}, []int{3, 2, 2, 2, 3}, []int{3, 2, 2, 2, 3}},
		"stopBox":               []any{[]int{3, 0, 0, 0, 3}, []int{3, 2, 2, 2, 3}, []int{3, 2, 2, 2, 3}, []int{3, 2, 2, 2, 3}},
		"versions": map[string]any{
			"server_core": "1.1",
			"server_game": "1.0",
			"server_math": "1.0",
		},
		"winValidation": map[string]any{
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
			"needcheck":                    false,
			"period":                       86400000,
			"remaintime":                   86400000,
			"winlimit_fictiveRotate_gcurr": 25000000,
		},
		"winlimits": map[string]any{
			"maxWinLimitK":         7500,
			"maxWin_gcurr":         nil,
			"needControlJackpot":   false,
			"winLimitK_gameconfig": 7500,
		},

		"info_adv_games": map[string]any{
			"hint": 0,
			"list": []any{},
			"nnew": 0,
		},
		"newOpenGames": []any{},
		"ss":           map[string]any{},
		"useracc": map[string]any{
			"altcurr":      "",
			"amount":       100000,
			"currency":     "",
			"currencyType": 2,
			"currencyUnit": 1,
			"symbol_first": "0",
		},
		"uservars": map[string]any{
			"language": "en",
		},
	}

	b, _ := json.Marshal(gs)
	return string(b)
}

func (m m400123) InputCoef(ctl int32) int32 {
	return 100
}

func (m m400123) MinPayout(ctl int32) int32 {
	mode, ok := m.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * m.Line()
}

func cloneSlice400123(src [][]int16) [][]int16 {
	dst := make([][]int16, len(src))
	for i := range src {
		dst[i] = make([]int16, len(src[i]))
		copy(dst[i], src[i])
	}
	return dst
}

func (m m400123) makeMaskGrid() [][]int16 {
	gridRows := make([][]int16, m.Config.Row)
	for row := 0; row < m.Config.Row; row++ {
		gridRows[row] = make([]int16, m.Config.Column)
		for col := 0; col < m.Config.Column; col++ {
			gridRows[row][col] = 0
		}
	}
	return gridRows
}
